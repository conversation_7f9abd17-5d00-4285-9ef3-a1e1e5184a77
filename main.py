
import sensor
import image
import lcd
import KPU as kpu
import time
from Maix import FPIOA, GPIO
import gc
from fpioa_manager import fm
from board import board_info
import utime
from machine import UART
import json

# ==================== 配置参数 ====================
# 模型文件路径
FACE_DETECTION_MODEL = "/sd/FaceDetection.smodel"
FACE_LANDMARK_MODEL = "/sd/FaceLandmarkDetection.smodel"
FEATURE_EXTRACTION_MODEL = "/sd/FeatureExtraction.smodel"
FACE_DATA_FILE = "/sd/face_data.json"

# 识别参数
ACCURACY_THRESHOLD = 85    # 识别准确度阈值
DUPLICATE_THRESHOLD = 80   # 重复人脸检测阈值
RECOGNITION_TIMEOUT = 10000 # 识别超时时间(ms)

# 串口配置
UART_TX_PIN = 31          
UART_RX_PIN = 30          
UART_BAUDRATE = 115200    # 波特率
UART_BUFFER_SIZE = 4096

# 串口命令
CMD_START = "START"       # STM32发送开始识别命令
CMD_FAIL = "FAIL"         # K210发送识别失败命令
# 识别成功将发送具体的用户ID，格式: "ID:X" (X为用户编号)

# 按键配置
BOUNCE_PROTECTION = 50    # 按键防抖延时(ms)

# 人脸检测配置
FACE_ANCHOR = (1.889, 2.5245, 2.9465, 3.94056, 3.99987, 5.3658, 5.155437, 6.92275, 6.718375, 9.01025)
FACE_KEY_POINTS = [(44, 59), (84, 59), (64, 82), (47, 105), (81, 105)]
FACE_SIZE = (128, 128)

# 默认用户名
DEFAULT_NAMES = ['User1', 'User2', 'User3', 'User4', 'User5', 'User6', 'User7', 'User8', 'User9', 'User10']

# ==================== 硬件初始化 ====================
# 串口初始化
fm.register(UART_TX_PIN, fm.fpioa.UART2_TX, force=True)
fm.register(UART_RX_PIN, fm.fpioa.UART2_RX, force=True)
uart = UART(UART.UART2, UART_BAUDRATE, read_buf_len=UART_BUFFER_SIZE)

# 模型加载
print("正在加载人脸识别模型...")
task_fd = kpu.load(FACE_DETECTION_MODEL)
task_ld = kpu.load(FACE_LANDMARK_MODEL)
task_fe = kpu.load(FEATURE_EXTRACTION_MODEL)
print("模型加载完成")

clock = time.clock()

# 按键初始化
fm.register(board_info.BOOT_KEY, fm.fpioa.GPIOHS0)
key_gpio = GPIO(GPIO.GPIOHS0, GPIO.IN)
start_processing = False

def set_key_state(*_):
    global start_processing
    start_processing = True
    print("检测到BOOT键按下，准备录入人脸")
    utime.sleep_ms(BOUNCE_PROTECTION)

key_gpio.irq(set_key_state, GPIO.IRQ_RISING, GPIO.WAKEUP_NOT_SUPPORT)

# 摄像头和显示初始化
lcd.init()
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)
sensor.set_hmirror(1)
sensor.set_vflip(1)
sensor.run(1)

# 人脸检测初始化
kpu.init_yolo2(task_fd, 0.5, 0.3, 5, FACE_ANCHOR)
img_lcd = image.Image()
img_face = image.Image(size=FACE_SIZE)
img_face.pix_to_ai()

# 人脸数据存储
record_ftr = []
record_ftrs = []
names = list(DEFAULT_NAMES)

# ==================== 功能函数 ====================
def save_face_data():
    """保存人脸数据到SD卡"""
    try:
        data = {"features": record_ftrs, "names": names[:len(record_ftrs)]}
        with open(FACE_DATA_FILE, 'w') as f:
            json.dump(data, f)
        print("人脸数据已保存到SD卡")
        return True
    except Exception as e:
        print("保存人脸数据失败:", e)
        return False

def load_face_data():
    """从SD卡加载人脸数据"""
    global record_ftrs, names
    try:
        with open(FACE_DATA_FILE, 'r') as f:
            data = json.load(f)

        if "features" in data:
            record_ftrs = data["features"]
        if "names" in data:
            loaded_names = data["names"]
            for i in range(len(loaded_names)):
                if i < len(names):
                    names[i] = loaded_names[i]

        print("已加载 %d 个人脸数据" % len(record_ftrs))
        return True
    except:
        print("未找到人脸数据文件，使用默认设置")
        return False

def wait_for_uart_command():
    """等待STM32发送开始识别命令"""
    print("START")
    while True:
        data = uart.read()
        if data:
            try:
                cmd = data.decode('utf-8').strip()
                if CMD_START in cmd:
                    print("收到STM32开始识别命令: %s" % cmd)
                    return True
            except:
                pass
        time.sleep_ms(20)

def send_result_to_stm32(success, user_id=None):
    """发送识别结果给STM32"""
    if success and user_id is not None:
        # 发送用户ID，格式: "ID:X"
        cmd = "ID:%d" % user_id
        uart.write("%s\n" % cmd)
        print("发送识别成功，用户ID给STM32: %s" % cmd)
    else:
        uart.write("%s\n" % CMD_FAIL)
        print("发送识别失败命令给STM32: %s" % CMD_FAIL)

def extract_face_feature(img, detection):
    """提取人脸特征"""
    i = detection
    face_cut = img.cut(i.x(), i.y(), i.w(), i.h())
    face_cut_128 = face_cut.resize(128, 128)
    face_cut_128.pix_to_ai()

    # 人脸关键点检测
    fmap = kpu.forward(task_ld, face_cut_128)
    plist = fmap[:]
    le = (i.x() + int(plist[0] * i.w() - 10), i.y() + int(plist[1] * i.h()))
    re = (i.x() + int(plist[2] * i.w()), i.y() + int(plist[3] * i.h()))
    nose = (i.x() + int(plist[4] * i.w()), i.y() + int(plist[5] * i.h()))
    lm = (i.x() + int(plist[6] * i.w()), i.y() + int(plist[7] * i.h()))
    rm = (i.x() + int(plist[8] * i.w()), i.y() + int(plist[9] * i.h()))

    # 人脸对齐
    src_point = [le, re, nose, lm, rm]
    T = image.get_affine_transform(src_point, FACE_KEY_POINTS)
    image.warp_affine_ai(img, img_face, T)
    img_face.ai_to_pix()

    # 特征提取
    fmap = kpu.forward(task_fe, img_face)
    feature = kpu.face_encode(fmap[:])

    del face_cut_128
    return feature

# ==================== 主程序 ====================
print("系统初始化完成")
load_face_data()
print("K210人脸识别系统启动")
print("功能说明:")
print("1. 按BOOT键录入人脸")
print("2. STM32发送'%s'命令开始识别" % CMD_START)
print("3. 识别成功发送'ID:X'(X为用户编号)，失败发送'%s'" % CMD_FAIL)

# 人脸录入模式
print("\n=== 人脸录入模式 ===")
print("请按BOOT键录入要识别的人脸")

while True:
    # 第一阶段：人脸录入模式
    img = sensor.snapshot()
    clock.tick()

    # 一直显示提示信息
    img.draw_string(10, 200, "Press BOOT to save face", color=(255, 255, 0), scale=2)
    img.draw_string(10, 10, "Faces saved: %d" % len(record_ftrs), color=(0, 255, 0), scale=2)

    code = kpu.run_yolo2(task_fd, img)

    if code:
        for i in code:
            # 绘制人脸框
            img.draw_rectangle(i.rect())

            # 按BOOT键录入人脸
            if start_processing:
                print("开始录入人脸...")
                feature = extract_face_feature(img, i)

                # 检查是否重复
                is_duplicate = False
                for existing_feature in record_ftrs:
                    similarity = kpu.face_compare(existing_feature, feature)
                    if similarity > DUPLICATE_THRESHOLD:
                        is_duplicate = True
                        print("人脸已存在，相似度: %2.1f" % similarity)
                        break

                if not is_duplicate:
                    record_ftrs.append(feature)
                    face_count = len(record_ftrs)
                    # 确保names列表足够长
                    while len(names) < face_count:
                        names.append("User%d" % (len(names)+1))

                    save_face_data()
                    print("人脸录入成功，编号: %s" % names[face_count-1])
                    img.draw_string(10, 30, "Saved: %s" % names[face_count-1], color=(0, 255, 255), scale=2)
                else:
                    img.draw_string(10, 30, "Face exists!", color=(255, 0, 0), scale=2)

                start_processing = False
            break

    lcd.display(img)

    # 检查是否有足够的人脸数据，如果有则进入识别模式
    if len(record_ftrs) > 0:
        # 检查是否收到STM32的开始识别命令
        data = uart.read()
        if data:
            try:
                cmd = data.decode('utf-8').strip()
                if CMD_START in cmd:
                    print("\n=== 收到STM32开始识别命令 ===")
                    print("开始人脸识别...")

                    # 第二阶段：人脸识别模式
                    recognition_success = False
                    start_time = time.ticks_ms()

                    while not recognition_success:
                        img = sensor.snapshot()
                        code = kpu.run_yolo2(task_fd, img)

                        if code:
                            for i in code:
                                img.draw_rectangle(i.rect())
                                feature = extract_face_feature(img, i)

                                # 人脸比对
                                max_score = 0
                                index = 0
                                if len(record_ftrs) > 0:
                                    scores = []
                                    for j in range(len(record_ftrs)):
                                        score = kpu.face_compare(record_ftrs[j], feature)
                                        scores.append(score)

                                    for k in range(len(scores)):
                                        if max_score < scores[k]:
                                            max_score = scores[k]
                                            index = k

                                # 显示识别结果
                                if max_score > ACCURACY_THRESHOLD:
                                    img.draw_string(i.x(), i.y(), ("%s :%2.1f" % (names[index], max_score)),
                                                   color=(0, 255, 0), scale=2)
                                    recognition_success = True
                                    user_id = index + 1  # 用户ID从1开始
                                    print("人脸识别成功: %s, 用户ID: %d, 相似度: %2.1f" % (names[index], user_id, max_score))
                                    send_result_to_stm32(True, user_id)
                                    break
                                else:
                                    img.draw_string(i.x(), i.y(), ("X :%2.1f" % max_score),
                                                   color=(255, 0, 0), scale=2)

                        lcd.display(img)

                        # 超时检查
                        if time.ticks_diff(time.ticks_ms(), start_time) > RECOGNITION_TIMEOUT:
                            print("人脸识别超时")
                            send_result_to_stm32(False, None)
                            break

                    if not recognition_success:
                        print("人脸识别失败")

                    print("返回人脸录入模式\n")
            except:
                pass

    gc.collect()

# 程序结束时清理资源
# kpu.deinit(task_fe)
# kpu.deinit(task_ld)
# kpu.deinit(task_fd)
